<template>
  <q-dialog @update:model-value="val => $emit('update:modelValue', val)" persistent>
    <q-card class="column no-wrap"
            style="min-width: 800px; height: 800px;">
      <q-toolbar class="bg-animate q-py-md bg-backgroundSecondary">
        <h5 style="margin: 0;">{{ $t('afaktoApp.buyer.home.titleCreate') }}</h5>
        <q-space />

        <q-btn class="buttonNeutral" icon="close" to="#" @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

          <div class="row no-wrap fit">
            <div style="width: 30%; flex-shrink: 0;">
              <q-stepper
                v-model="tab"
                vertical
                color="primary"
                ref="stepper"
                animated
                class="fit"
                style="border-radius: 0px"
              >
                <q-step
                  :name="'main'"
                  prefix="1"
                  :title="t('afaktoApp.buyer.create.basicInformation')"
                />
                <q-step
                  :name="'onlineData'"
                  prefix="2"
                  :title="t('afaktoApp.buyer.create.onlineData')"
                />
                <q-step
                  :name="'buyer-details'"
                  prefix="3"
                  :title="t('afaktoApp.buyer.create.buyerDetails')"
                />
              </q-stepper>
            </div>

              <q-tab-panels v-model="tab" animated :vertical="$q.platform.is?.desktop" style="width: 100%">
                <q-tab-panel name="main">
                  <q-form ref="formRef" style="display: contents">

                    <h6 style="margin-top: 1em; margin-bottom: 0">{{t('afaktoApp.buyer.create.basicInformation')}}</h6>
                    <p class="text-green">Let's start with some basic information</p>
                    <!--                    <div class="enrich-tooltip">-->
                    <!--                      <p>-->
                    <!--                        {{ $t('afaktoApp.buyer.create.enrichTooltip') }}-->
                    <!--                      </p>-->
                    <!--                    </div>-->
                    <div class="q-col-gutter-md">
                      <q-select
                        id="company-select"
                        v-model="entity.company"
                        class="q-pa-none"
                        filled
                        hide-bottom-space
                        :options="useAuthenticationStore().account.companies"
                        option-label="name"
                        option-value="id"
                        :label="entity.company ? '' : $t('afaktoApp.buyer.company')"
                        :rules="[required]"
                      />

                      <q-select
                        v-model="entity.address.country"
                        emit-value
                        filled
                        hide-bottom-space
                        :label="entity.address.country ? '' : $t('afaktoApp.contract.country')"
                        id="country-select"
                        map-options
                        :options="countries.filter(c => c.iso)"
                        option-label="name"
                        option-value="code"
                        :rules="[required]"
                      >
                        <template v-if="entity.address.country" #prepend>
                          <span :class="`fi fi-${entity.address.country}`"></span>
                        </template>
                        <template #option="scope">
                          <q-item v-bind="scope.itemProps" dense>
                            <q-item-section side>
                              <span :class="`fi fi-${scope.opt.code}`" />
                            </q-item-section>
                            <q-item-section>
                              <q-item-label>{{ scope.opt.name }}</q-item-label>
                            </q-item-section>
                          </q-item>
                        </template>
                      </q-select>

                    <b-input
                      v-model="entity.code"
                      class="required"
                      hide-bottom-space
                      :label="$t('afaktoApp.buyer.code')"
                      :rules="[required]"
                    />
                    <b-input
                      v-model="entity.name"
                      class="required"
                      hide-bottom-space
                      :label="$t('afaktoApp.buyer.name')"
                      :rules="[required]"
                    />
                    </div>

                  </q-form>
                </q-tab-panel>

                <q-tab-panel name="onlineData">
                  {{getLegalEntity()}}
                  <q-form ref="formRef" style="display: contents">
                    <div v-if="!searchLegalEntity">
                      <div class="enrich-div">
                        <div class="row items-center justify-around q-my-sm full-width full-height">
                        <div>{{ filteredLegalEntities.length }} companies found</div>
                        <q-input
                          filled
                          dense
                          debounce="300"
                          v-model="postalCodeFilter"
                          label="Filter by Postal Code"
                          style="max-width: 250px"
                        />
                        <q-btn
                          label="Manual"
                          color="secondary"
                          @click="goToNextStep()"
                        />
                      </div>
                        
                        <q-card
                          @click="copyEntity(row)"
                          v-for="row in filteredLegalEntities"
                          :key="row.number"
                          class="q-pa-md bg-brandLowest full-width "
                          bordered
                          style="cursor: pointer; border-radius: 0 !important;"
                          >

                          <div class="text-neutralHigher q-mb-sm text-weight-bolder" style="font-size: 18px">
                            {{ row.name }}
                          </div>
                          <div class="text-subtitle2 text-neutralHigh">
                            <div class="flex items-center q-gutter-x-xs">
                              <div>
                                <q-icon name="description" class="text-h6" />
                                {{ row.number }}
                              </div>
                            </div>
                          </div>
                          <div class="text-subtitle2 text-neutralHigh">
                            <div class="flex items-center q-gutter-x-xs overflow-hidden no-wrap ellipsis">
                              <q-icon name="home" class="text-h6" />
                              <div>
                                {{ row.address?.streetName }},
                                {{ row.address?.city }},
                                {{ row.address?.postalCode }}
                              </div>
                            </div>
                          </div>
                        </q-card>

                      </div>
                    </div>

                    <div v-else>Loading...</div>
                  </q-form>
                </q-tab-panel>


                <q-tab-panel name="buyer-details">
                  <q-form ref="formRef" style="display: contents">
                    <q-input
                      v-model="entity.number"
                      class="required"
                      :fill-mask="['SIREN', 'SIRET'].includes(entity.numberType)"
                      filled
                      :label="$t('afaktoApp.buyer.number')"
                      :mask="NUMBER_TYPE_MASKS[entity.numberType]"
                      :rules="[required]"
                      unmasked-value
                    >
                      <template #prepend>
                        <q-select
                          v-model="entity.numberType"
                          bg-color="transparent"
                          class="required"
                          :label="$t('afaktoApp.buyer.numberType')"
                          :options="NUMBER_TYPES"
                          :option-label="numberType => $t(`afaktoApp.NumberType.${numberType}`).replaceAll('_', ' ')"
                          :rules="[required]"
                          style="min-width: 12em"
                        />
                      </template>
                    </q-input>


                    <q-toggle
                      v-model="entity.excluded"
                      :label="$t('afaktoApp.buyer.excluded') + ' - ' + $t('afaktoApp.buyer.excluded_help')"
                    />
                    <q-icon color="warning" right name="remove_done" size="sm" style="vertical-align: baseline" />
                    <q-input
                      v-if="entity.excluded"
                      v-model="entity.exclusionReason"
                      filled
                      :label="t('afaktoApp.buyer.exclusionReason')"
                      rows="2"
                      type="textarea"
                    />

                    <address-comp v-model="entity.address" />
                    <contact-comp v-model="entity.contact"/>
                  </q-form>
                </q-tab-panel>
              </q-tab-panels>
          </div>

      <div class="q-pa-sm row justify-between items-center q-gutter-sm bg-backgroundSecondary"
           style="margin-top: auto;">
        <q-btn
          class="buttonNeutral bg-neutralLowest"
          :label="$t('entity.action.cancel')"
          to="#"
          @click="closeDialog"
        />

        <!-- Right side: step buttons and save -->
        <div class="q-gutter-sm">
          <q-btn
            class="bg-neutralLowest"
            label="<"
            :disable="isFirstStep()"
            @click="goToPreviousStep"
          />
          <q-btn
            class="bg-neutralLowest"
            label=">"
            :disable="isLastStep()"
            @click="goToNextStep"
          />
          <q-btn
            :disable="!isLastStep()"
            @click="goToNextStep"
            class="buttonBrand"
            :label="$t('entity.action.save')"
            type="submit"
          />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from 'quasar';
import {computed, onMounted, ref} from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import BuyerService from 'pages/entities/buyer/buyer.service';
import { required } from 'src/components/rules';
import { NUMBER_TYPES, NUMBER_TYPE_MASKS } from 'src/constants/numberType';
import EntityMeta from 'src/pages/subcomponents/EntityMeta.vue';
import EntityMetaDates from 'src/pages/subcomponents/EntityMetaDates.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications';
import AddressComp from '../../subcomponents/AddressComp.vue';
import ContactComp from '../../subcomponents/ContactComp.vue';
import BuyerDetailsFromInsurer from './BuyerDetailsFromInsurer.vue';
import BuyerDetailsInvoices from './BuyerDetailsInvoices.vue';
import CreditLimitRequestsTable from '../credit-limit-request/CreditLimitRequestsTable.vue';
import ContractInsuranceTable from '../insurance/ContractInsuranceTable.vue';

const { notifyError } = useNotifications();
import countries from 'flag-icons/country.json';
import {useBuyersListStore} from "src/stores/buyers-list-store.js";
const $q = useQuasar();
const route = useRoute();
const { t } = useI18n();
const tab = ref(route.query.tab || 'main');
const emit = defineEmits(['update:modelValue']);

const formRef = ref(null);

const stepNames = ['main', 'onlineData', 'buyer-details']

const columns = [
  { name: 'name', label: 'Name', field: 'name', align: 'left' },
  { name: 'number', label: 'Number', field: 'number' },
  { name: 'city', label: 'City', field: row => row.address?.city },
  { name: 'street', label: 'Street', field: row => row.address?.streetName },
  { name: 'postalCode', label: 'Postal Code', field: row => row.address?.postalCode },
];

const searchLegalEntity = ref(false);
const legalEntities = ref([]);

const getLegalEntity = () => {
  if (!props.entity.name)
    return

  searchLegalEntity.value = true;
  legalEntities.value = [];

  BuyerService.search(props.entity)
    .then(enrichedBuyers => {
      legalEntities.value = enrichedBuyers.data;
    })
    .catch(err => {
        notifyError(err);
    })
    .finally(() => (searchLegalEntity.value = false));
};


const postalCodeFilter = ref('')

const filteredLegalEntities = computed(() => {
  if (!postalCodeFilter.value) return legalEntities.value
  return legalEntities.value.filter(entity =>
    entity.address?.postalCode?.toString().startsWith(postalCodeFilter.value)
  )
})

const copyEntity = (row) => {
  props.entity.address = row.address;
  props.entity.number = row.number;
  props.entity.numberType = row.numberType
  goToNextStep()
}

const onSubmit = () => {
  BuyerService.save(props.entity)
    .then(() => {
      useBuyersListStore().markUpdated();
      closeDialog()
    })
    .catch(error => notifyError(error));
};

const closeDialog = () => {
  emit('update:modelValue', false);
};

const stepper = ref(null);

function isFirstStep() {
  return stepNames.indexOf(tab.value) === 0
}

function isLastStep() {
  return stepNames.indexOf(tab.value) === stepNames.length - 1
}

async function goToNextStep() {
  if (formRef.value?.validate) {
    const valid = await formRef.value.validate()
    if (!valid) return
  }

  if (isLastStep())
    return onSubmit();

  if (!isLastStep())
    stepper.value?.next()
}

function goToPreviousStep() {
  if (!isFirstStep()) {
    stepper.value?.previous()
  }
}

const props = defineProps({
  entity: Object,
});

</script>

<style lang="scss">

.enrich-div {
  border: solid 1px $borderPrimary;
  border-radius: 8px;
}

</style>
